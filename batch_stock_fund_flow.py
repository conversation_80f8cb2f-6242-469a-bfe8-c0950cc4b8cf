#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量股票资金流向数据获取程序（增强版）
读取CSV文件中的股票代码，批量获取资金流向数据并保存

功能特性：
1. 重试机制：每个股票代码最多重试10次，重试间隔3秒
2. 数据去重合并：自动合并现有文件数据，按日期去重
3. 稳定性增强：确保每个股票代码都能成功获取数据
"""

import akshare as ak
import pandas as pd
import os
import time
from datetime import datetime

def parse_secid(secid):
    """
    解析secID，提取股票代码和市场代码
    
    Args:
        secid (str): 格式如 "000001.XSHE" 的股票ID
    
    Returns:
        tuple: (股票代码, 市场代码) 如 ("000001", "sz")
    """
    if '.' not in secid:
        return None, None
    
    stock_code, exchange = secid.split('.')
    
    # 根据交易所代码转换为akshare需要的市场代码
    market_mapping = {
        'XSHG': 'sh',  # 上海证券交易所
        'XSHE': 'sz',  # 深圳证券交易所
        'XBEI': 'bj'   # 北京证券交易所
    }
    
    market_code = market_mapping.get(exchange, None)
    return stock_code, market_code

def create_output_directory():
    """
    创建输出目录
    
    Returns:
        str: 输出目录路径
    """
    output_dir = "stock_fund_flow_data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    return output_dir

def get_single_stock_fund_flow(stock_code, market_code, max_retries=10, retry_interval=3):
    """
    获取单只股票的资金流向数据（带重试机制）

    Args:
        stock_code (str): 股票代码
        market_code (str): 市场代码
        max_retries (int): 最大重试次数，默认10次
        retry_interval (int): 重试间隔秒数，默认3秒

    Returns:
        pd.DataFrame or None: 资金流向数据
    """
    for attempt in range(max_retries + 1):  # +1 是因为第一次不算重试
        try:
            if attempt == 0:
                print(f"正在获取 {stock_code}.{market_code} 的资金流向数据...")
            else:
                print(f"第 {attempt} 次重试获取 {stock_code}.{market_code} 的数据...")

            # 调用akshare接口获取数据
            fund_flow_df = ak.stock_individual_fund_flow(stock=stock_code, market=market_code)

            if fund_flow_df is not None and not fund_flow_df.empty:
                print(f"成功获取 {stock_code}.{market_code} 数据，共 {len(fund_flow_df)} 条记录")
                return fund_flow_df
            else:
                print(f"警告: {stock_code}.{market_code} 未获取到数据")
                if attempt < max_retries:
                    print(f"等待 {retry_interval} 秒后重试...")
                    time.sleep(retry_interval)
                    continue
                else:
                    print(f"已达到最大重试次数 ({max_retries})，放弃获取 {stock_code}.{market_code}")
                    return None

        except Exception as e:
            print(f"错误: 获取 {stock_code}.{market_code} 数据失败 - {str(e)}")
            if attempt < max_retries:
                print(f"等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                continue
            else:
                print(f"已达到最大重试次数 ({max_retries})，放弃获取 {stock_code}.{market_code}")
                return None

    return None

def save_stock_data(df, stock_code, market_code, output_dir):
    """
    保存单只股票的数据到CSV文件（带数据去重合并功能）

    Args:
        df (pd.DataFrame): 股票数据
        stock_code (str): 股票代码
        market_code (str): 市场代码
        output_dir (str): 输出目录
    """
    if df is None or df.empty:
        return

    try:
        # 生成文件名
        filename = f"{stock_code}_{market_code}_fund_flow.csv"
        filepath = os.path.join(output_dir, filename)

        # 检查文件是否已存在
        if os.path.exists(filepath):
            print(f"发现已存在文件: {filepath}")
            try:
                # 读取现有数据
                existing_df = pd.read_csv(filepath, encoding='utf-8-sig')
                print(f"读取现有数据，共 {len(existing_df)} 条记录")

                # 合并新旧数据
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                print(f"合并后数据，共 {len(combined_df)} 条记录")

                # 检查是否有日期列用于去重
                date_columns = ['日期', 'date', 'Date', '交易日期', 'trade_date']
                date_col = None
                for col in date_columns:
                    if col in combined_df.columns:
                        date_col = col
                        break

                if date_col:
                    # 按日期去重，保留最后出现的记录（即新数据优先）
                    before_dedup = len(combined_df)
                    combined_df = combined_df.drop_duplicates(subset=[date_col], keep='last')
                    after_dedup = len(combined_df)
                    print(f"按 '{date_col}' 列去重，删除 {before_dedup - after_dedup} 条重复记录")
                else:
                    print("未找到日期列，跳过去重处理")

                # 保存合并去重后的数据
                final_df = combined_df

            except Exception as e:
                print(f"读取或合并现有文件失败: {str(e)}，将直接保存新数据")
                final_df = df
        else:
            print(f"文件不存在，将创建新文件: {filepath}")
            final_df = df

        # 保存为CSV文件，使用utf-8-sig编码确保中文正确显示
        final_df.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filepath}，共 {len(final_df)} 条记录")

    except Exception as e:
        print(f"错误: 保存 {stock_code}.{market_code} 数据失败 - {str(e)}")

def read_stock_list(csv_file_path):
    """
    读取股票列表CSV文件
    
    Args:
        csv_file_path (str): CSV文件路径
    
    Returns:
        list: 股票ID列表
    """
    try:
        df = pd.read_csv(csv_file_path, encoding='utf-8')
        if 'secID' in df.columns:
            stock_list = df['secID'].tolist()
            print(f"成功读取股票列表，共 {len(stock_list)} 只股票")
            return stock_list
        else:
            print("错误: CSV文件中未找到 'secID' 列")
            return []
    except Exception as e:
        print(f"错误: 读取CSV文件失败 - {str(e)}")
        return []

def main():
    """
    主函数
    """
    print("=" * 60)
    print("批量股票资金流向数据获取程序（增强版）")
    print("=" * 60)
    print("功能特性：")
    print("• 重试机制：每个股票最多重试10次，间隔3秒")
    print("• 数据去重：自动合并现有文件，按日期去重")
    print("• 稳定性增强：确保数据获取成功率")
    print("=" * 60)
    
    # CSV文件路径
    csv_file_path = r"d:\Andy\coding\gupiao_huice\secid.csv"
    
    # 检查文件是否存在
    if not os.path.exists(csv_file_path):
        print(f"错误: 文件不存在 - {csv_file_path}")
        return
    
    # 读取股票列表
    stock_list = read_stock_list(csv_file_path)
    if not stock_list:
        print("未能读取到股票数据，程序退出")
        return
    
    # 创建输出目录
    output_dir = create_output_directory()
    
    # 统计信息
    total_stocks = len(stock_list)
    success_count = 0
    failed_count = 0
    
    print(f"\n开始处理 {total_stocks} 只股票...")
    print("-" * 60)
    
    # 逐个处理股票
    for i, secid in enumerate(stock_list, 1):
        print(f"\n[{i}/{total_stocks}] 处理股票: {secid}")
        
        # 解析股票代码和市场代码
        stock_code, market_code = parse_secid(secid.strip())
        
        if stock_code is None or market_code is None:
            print(f"跳过: 无法解析的股票ID - {secid}")
            failed_count += 1
            continue
        
        # 获取股票数据
        fund_flow_data = get_single_stock_fund_flow(stock_code, market_code)
        
        if fund_flow_data is not None:
            # 保存数据
            save_stock_data(fund_flow_data, stock_code, market_code, output_dir)
            success_count += 1
        else:
            failed_count += 1
        
        # 添加延时，避免请求过于频繁
        if i < total_stocks:  # 最后一个不需要延时
            print("等待3秒...")
            time.sleep(3)
    
    # 输出统计结果
    print("\n" + "=" * 60)
    print("处理完成！统计结果:")
    print(f"总股票数: {total_stocks}")
    print(f"成功获取: {success_count}")
    print(f"失败数量: {failed_count}")
    print(f"成功率: {success_count/total_stocks*100:.1f}%")
    print(f"数据保存目录: {os.path.abspath(output_dir)}")
    print("=" * 60)

if __name__ == "__main__":
    main()